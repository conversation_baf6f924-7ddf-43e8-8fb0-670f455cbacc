{"name": "cashlog-nextjs", "version": "0.3.5", "private": true, "scripts": {"dev": "next dev --turbopack", "build:local": "next build", "build": "NODE_OPTIONS=\"--max-old-space-size=4096\" next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix"}, "dependencies": {"@heroui/alert": "^2.2.19", "@heroui/breadcrumbs": "^2.2.13", "@heroui/button": "2.2.17", "@heroui/card": "^2.2.18", "@heroui/checkbox": "^2.3.18", "@heroui/chip": "^2.2.15", "@heroui/code": "2.2.12", "@heroui/date-input": "^2.3.18", "@heroui/date-picker": "^2.3.19", "@heroui/dropdown": "^2.3.21", "@heroui/input": "2.4.17", "@heroui/kbd": "2.2.13", "@heroui/link": "2.2.14", "@heroui/listbox": "2.3.16", "@heroui/modal": "^2.2.18", "@heroui/navbar": "^2.2.20", "@heroui/pagination": "^2.2.21", "@heroui/radio": "^2.3.16", "@heroui/scroll-shadow": "^2.3.13", "@heroui/select": "^2.4.17", "@heroui/skeleton": "^2.2.12", "@heroui/snippet": "2.2.18", "@heroui/switch": "2.2.15", "@heroui/system": "2.4.13", "@heroui/table": "^2.2.18", "@heroui/theme": "2.4.13", "@heroui/toast": "^2.0.9", "@heroui/tooltip": "^2.2.18", "@lottiefiles/dotlottie-react": "^0.14.3", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.21", "@react-stately/data": "^3.12.3", "@types/lodash": "^4.17.20", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "clsx": "2.1.1", "cookies-next": "^6.0.0", "crypto": "^1.0.1", "formidable": "^3.5.4", "framer-motion": "11.13.1", "intl-messageformat": "^10.5.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "motion": "^12.23.3", "mysql2": "^3.14.1", "next": "15.0.4", "next-themes": "^0.4.4", "openai": "^4.98.0", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.3.1", "react-icons": "^5.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}